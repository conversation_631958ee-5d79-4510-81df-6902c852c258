using UnityEngine;

public class API16Bounds : MonoBehaviour
{
    [SerializeField] private Vector3 size = new Vector3(2, 2, 2);
    [SerializeField] private Vector3 center;
    private Bounds bounds;
    [SerializeField] private Transform target;

    //用于绘制新Bounds
    private Vector3 currentCenter;
    private Bounds currentBounds;

    void Start()
    {
        center = transform.position;
        bounds = new Bounds(center, size);

        if (target == null)
        {
            Debug.LogWarning("请在Inspector面板中为target赋值");
            return;
        }

        if (bounds.Contains(target.position))
        {
            Debug.Log("目标在Bounds内");
        }
        else
        {
            bounds.Encapsulate(target.position);
            size = bounds.size;
            Debug.Log("目标不在Bounds内，已扩展Bounds");
        }

    }


    void OnDrawGizmos()
    {
        // 更新bounds以反映当前的transform位置
        currentCenter = transform.position;
        currentBounds = new Bounds(currentCenter, size);

        // 绘制主要的Bounds立方体
        Gizmos.color = Color.green;
        Gizmos.DrawWireCube(currentBounds.center, currentBounds.size);
        Gizmos.color = new Color(0, 1, 0, 0.2f);
        Gizmos.DrawCube(currentBounds.center, currentBounds.size);

        // 绘制中心点
        Gizmos.color = Color.yellow;
        Gizmos.DrawSphere(currentBounds.center, 0.1f);

        // 绘制min点（最小角点）
        Gizmos.color = Color.red;
        Gizmos.DrawSphere(currentBounds.min, 0.1f);

        // 绘制max点（最大角点）
        Gizmos.color = Color.blue;
        Gizmos.DrawSphere(currentBounds.max, 0.1f);

        // 绘制extents（从中心到各个面的距离）
        Gizmos.color = Color.cyan;
        Vector3 extents = currentBounds.extents;

        // X轴extents
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.right * extents.x);
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.left * extents.x);

        // Y轴extents
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.up * extents.y);
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.down * extents.y);

        // Z轴extents
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.forward * extents.z);
        Gizmos.DrawLine(currentBounds.center, currentBounds.center + Vector3.back * extents.z);
    }
}
