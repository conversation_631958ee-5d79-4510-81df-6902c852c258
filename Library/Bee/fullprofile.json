{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 85224, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 85224, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 85224, "tid": 853, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 85224, "tid": 853, "ts": 1753863073911557, "dur": 260, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073913172, "dur": 587, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 85224, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 85224, "tid": 1, "ts": 1753863073588733, "dur": 5148, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 85224, "tid": 1, "ts": 1753863073593884, "dur": 43047, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 85224, "tid": 1, "ts": 1753863073636943, "dur": 44974, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073913764, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 85224, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073587195, "dur": 891, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073588090, "dur": 318597, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073588618, "dur": 1792, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073590414, "dur": 879, "ph": "X", "name": "ProcessMessages 8121", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591295, "dur": 50, "ph": "X", "name": "ReadAsync 8121", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591359, "dur": 7, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591378, "dur": 42, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591447, "dur": 12, "ph": "X", "name": "ProcessMessages 2268", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591460, "dur": 99, "ph": "X", "name": "ReadAsync 2268", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591565, "dur": 3, "ph": "X", "name": "ProcessMessages 4342", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591570, "dur": 20, "ph": "X", "name": "ReadAsync 4342", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591600, "dur": 2, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591604, "dur": 30, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591637, "dur": 1, "ph": "X", "name": "ProcessMessages 1232", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591644, "dur": 20, "ph": "X", "name": "ReadAsync 1232", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591665, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591667, "dur": 32, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591718, "dur": 2, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591721, "dur": 34, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591757, "dur": 1, "ph": "X", "name": "ProcessMessages 1693", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591782, "dur": 38, "ph": "X", "name": "ReadAsync 1693", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591823, "dur": 5, "ph": "X", "name": "ProcessMessages 2187", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591830, "dur": 71, "ph": "X", "name": "ReadAsync 2187", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591902, "dur": 2, "ph": "X", "name": "ProcessMessages 2715", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591906, "dur": 47, "ph": "X", "name": "ReadAsync 2715", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591955, "dur": 2, "ph": "X", "name": "ProcessMessages 1666", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073591958, "dur": 88, "ph": "X", "name": "ReadAsync 1666", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592053, "dur": 3, "ph": "X", "name": "ProcessMessages 1812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592059, "dur": 42, "ph": "X", "name": "ReadAsync 1812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592112, "dur": 2, "ph": "X", "name": "ProcessMessages 2651", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592121, "dur": 32, "ph": "X", "name": "ReadAsync 2651", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592164, "dur": 1, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592167, "dur": 95, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592263, "dur": 2, "ph": "X", "name": "ProcessMessages 1824", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592266, "dur": 40, "ph": "X", "name": "ReadAsync 1824", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592308, "dur": 1, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592310, "dur": 109, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592421, "dur": 3, "ph": "X", "name": "ProcessMessages 3335", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592425, "dur": 44, "ph": "X", "name": "ReadAsync 3335", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592471, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592473, "dur": 21, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592503, "dur": 1, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592505, "dur": 23, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592531, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592533, "dur": 29, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592563, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592566, "dur": 21, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592588, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592590, "dur": 21, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592613, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592614, "dur": 24, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592641, "dur": 26, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592668, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592670, "dur": 29, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592700, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592702, "dur": 33, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592742, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592744, "dur": 31, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592777, "dur": 1, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592779, "dur": 20, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592801, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592802, "dur": 87, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592891, "dur": 2, "ph": "X", "name": "ProcessMessages 2113", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073592893, "dur": 289, "ph": "X", "name": "ReadAsync 2113", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593184, "dur": 6, "ph": "X", "name": "ProcessMessages 8160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593192, "dur": 18, "ph": "X", "name": "ReadAsync 8160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593213, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593249, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593252, "dur": 23, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593276, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593277, "dur": 18, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593302, "dur": 25, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593328, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593331, "dur": 32, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593364, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593366, "dur": 27, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593395, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593396, "dur": 32, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593430, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593432, "dur": 32, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593466, "dur": 28, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593497, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593499, "dur": 22, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593531, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593533, "dur": 29, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593565, "dur": 29, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593595, "dur": 1, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593597, "dur": 30, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593628, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593630, "dur": 75, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593707, "dur": 25, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593734, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593735, "dur": 34, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593778, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593781, "dur": 41, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593824, "dur": 1, "ph": "X", "name": "ProcessMessages 1451", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593826, "dur": 22, "ph": "X", "name": "ReadAsync 1451", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593852, "dur": 4, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593857, "dur": 24, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593883, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593885, "dur": 24, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593910, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593912, "dur": 18, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593931, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593933, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593968, "dur": 22, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593991, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073593993, "dur": 25, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594020, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594022, "dur": 24, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594049, "dur": 25, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594080, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594082, "dur": 20, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594103, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594105, "dur": 29, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594135, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594137, "dur": 26, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594164, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594165, "dur": 26, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594205, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594207, "dur": 27, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594235, "dur": 1, "ph": "X", "name": "ProcessMessages 1376", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594237, "dur": 20, "ph": "X", "name": "ReadAsync 1376", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594258, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594260, "dur": 34, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594295, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594296, "dur": 24, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594322, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594324, "dur": 19, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594344, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594346, "dur": 22, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594370, "dur": 22, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594394, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594395, "dur": 27, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594424, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594426, "dur": 23, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594450, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594451, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594479, "dur": 40, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594521, "dur": 1, "ph": "X", "name": "ProcessMessages 1616", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594523, "dur": 37, "ph": "X", "name": "ReadAsync 1616", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594561, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594563, "dur": 20, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594585, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594587, "dur": 24, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594614, "dur": 21, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594636, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594638, "dur": 21, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594660, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594662, "dur": 31, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594696, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594719, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594720, "dur": 21, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594742, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594744, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594764, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594765, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594787, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594807, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594809, "dur": 30, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594840, "dur": 4, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594845, "dur": 26, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594873, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594875, "dur": 25, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594901, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594903, "dur": 40, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594945, "dur": 2, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594948, "dur": 33, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594983, "dur": 1, "ph": "X", "name": "ProcessMessages 1123", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073594990, "dur": 34, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595025, "dur": 6, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595032, "dur": 24, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595057, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595059, "dur": 30, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595090, "dur": 2, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595093, "dur": 28, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595122, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595124, "dur": 19, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595144, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595146, "dur": 30, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595178, "dur": 1, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595180, "dur": 26, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595209, "dur": 84, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595294, "dur": 3, "ph": "X", "name": "ProcessMessages 2482", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595297, "dur": 20, "ph": "X", "name": "ReadAsync 2482", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595320, "dur": 24, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595345, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595347, "dur": 25, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595373, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595375, "dur": 22, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595399, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595422, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595423, "dur": 19, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595445, "dur": 31, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595479, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595481, "dur": 20, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595501, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595503, "dur": 19, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595523, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595525, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595576, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595593, "dur": 112, "ph": "X", "name": "ProcessMessages 1868", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595707, "dur": 16, "ph": "X", "name": "ReadAsync 1868", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595724, "dur": 3, "ph": "X", "name": "ProcessMessages 3574", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595728, "dur": 22, "ph": "X", "name": "ReadAsync 3574", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595751, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595753, "dur": 20, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595774, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595776, "dur": 18, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595796, "dur": 17, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595815, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595817, "dur": 77, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595895, "dur": 2, "ph": "X", "name": "ProcessMessages 2522", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595898, "dur": 26, "ph": "X", "name": "ReadAsync 2522", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595926, "dur": 1, "ph": "X", "name": "ProcessMessages 1504", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595933, "dur": 26, "ph": "X", "name": "ReadAsync 1504", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595960, "dur": 1, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595961, "dur": 36, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073595999, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596001, "dur": 22, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596024, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596026, "dur": 23, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596050, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596052, "dur": 21, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596075, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596102, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596103, "dur": 24, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596129, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596131, "dur": 30, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596162, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596163, "dur": 25, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596189, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596191, "dur": 20, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596221, "dur": 21, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596244, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596245, "dur": 22, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596269, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596271, "dur": 24, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596296, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596297, "dur": 26, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596325, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596327, "dur": 26, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596354, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596356, "dur": 19, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596377, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596378, "dur": 32, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596412, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596413, "dur": 17, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596431, "dur": 1, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596433, "dur": 17, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596453, "dur": 47, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596502, "dur": 1, "ph": "X", "name": "ProcessMessages 1495", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596504, "dur": 20, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596526, "dur": 20, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596549, "dur": 46, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596596, "dur": 4, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596601, "dur": 27, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596638, "dur": 20, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596659, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596661, "dur": 27, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596689, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596691, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596727, "dur": 37, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596765, "dur": 1, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596767, "dur": 44, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596813, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596814, "dur": 45, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596860, "dur": 1, "ph": "X", "name": "ProcessMessages 1328", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596863, "dur": 30, "ph": "X", "name": "ReadAsync 1328", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596894, "dur": 2, "ph": "X", "name": "ProcessMessages 1786", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596897, "dur": 31, "ph": "X", "name": "ReadAsync 1786", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596930, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596932, "dur": 58, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596992, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073596993, "dur": 59, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597054, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597056, "dur": 69, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597128, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597131, "dur": 30, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597170, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597172, "dur": 49, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597229, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597231, "dur": 47, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597279, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597281, "dur": 53, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597335, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597337, "dur": 69, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597407, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597409, "dur": 53, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597465, "dur": 48, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597514, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597515, "dur": 57, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597580, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597581, "dur": 41, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597625, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597627, "dur": 23, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597652, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597679, "dur": 51, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597733, "dur": 21, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597766, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597767, "dur": 46, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597815, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597817, "dur": 55, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597884, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597886, "dur": 54, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597942, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073597945, "dur": 55, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598001, "dur": 2, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598004, "dur": 75, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598081, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598083, "dur": 99, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598183, "dur": 2, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598186, "dur": 60, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598248, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598251, "dur": 35, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598288, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598290, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598317, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598320, "dur": 18, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598340, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598341, "dur": 21, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598364, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598365, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598397, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598399, "dur": 81, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598482, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598484, "dur": 22, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598534, "dur": 1, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598536, "dur": 27, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598564, "dur": 2, "ph": "X", "name": "ProcessMessages 1571", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598567, "dur": 49, "ph": "X", "name": "ReadAsync 1571", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598617, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598619, "dur": 66, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598687, "dur": 1, "ph": "X", "name": "ProcessMessages 1456", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598689, "dur": 19, "ph": "X", "name": "ReadAsync 1456", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598711, "dur": 23, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598738, "dur": 32, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598774, "dur": 40, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598815, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598818, "dur": 58, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598880, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598882, "dur": 60, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598948, "dur": 1, "ph": "X", "name": "ProcessMessages 1374", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598950, "dur": 34, "ph": "X", "name": "ReadAsync 1374", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598990, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073598992, "dur": 17, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599015, "dur": 45, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599061, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599063, "dur": 51, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599124, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599126, "dur": 51, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599179, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599180, "dur": 57, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599238, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599246, "dur": 55, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599302, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599304, "dur": 26, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599331, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599337, "dur": 50, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599388, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599390, "dur": 112, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599503, "dur": 2, "ph": "X", "name": "ProcessMessages 1812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599506, "dur": 34, "ph": "X", "name": "ReadAsync 1812", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599541, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599547, "dur": 30, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599586, "dur": 22, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599610, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599611, "dur": 58, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599671, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599673, "dur": 47, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599721, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599723, "dur": 60, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599784, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599786, "dur": 46, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599839, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599841, "dur": 47, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599890, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599891, "dur": 25, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599918, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599919, "dur": 46, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599968, "dur": 1, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073599970, "dur": 41, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600016, "dur": 27, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600045, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600047, "dur": 40, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600088, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600090, "dur": 23, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600114, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600117, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600147, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600174, "dur": 23, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600199, "dur": 22, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600224, "dur": 46, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600271, "dur": 1, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600273, "dur": 40, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600314, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600315, "dur": 19, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600336, "dur": 42, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600385, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600387, "dur": 58, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600447, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600449, "dur": 50, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600501, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600505, "dur": 89, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600595, "dur": 5, "ph": "X", "name": "ProcessMessages 1777", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600601, "dur": 15, "ph": "X", "name": "ReadAsync 1777", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600618, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600640, "dur": 27, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600668, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600670, "dur": 27, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600699, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600700, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600720, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600740, "dur": 57, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600799, "dur": 1, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600800, "dur": 25, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600827, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600829, "dur": 20, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600852, "dur": 17, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600871, "dur": 87, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600968, "dur": 2, "ph": "X", "name": "ProcessMessages 2055", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600971, "dur": 25, "ph": "X", "name": "ReadAsync 2055", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600997, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073600999, "dur": 59, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601061, "dur": 110, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601172, "dur": 212, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601385, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601428, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601431, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601475, "dur": 197, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601675, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601678, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601713, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601715, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601752, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601754, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601782, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601817, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601819, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601851, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073601993, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602021, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602058, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602060, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602108, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602110, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602183, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602291, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602292, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602317, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602360, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602401, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602434, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602571, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602618, "dur": 50, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602686, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602689, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602747, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602898, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602899, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602927, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073602961, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603048, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603050, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603161, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603163, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603219, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603222, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603258, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603260, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603306, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603329, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603420, "dur": 112, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603533, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603535, "dur": 74, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603612, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603700, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603701, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603772, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603778, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603810, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603844, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603885, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603887, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603934, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603940, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073603998, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604001, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604045, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604047, "dur": 101, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604150, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604153, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604198, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604199, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604263, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604266, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604374, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604409, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604411, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604449, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604528, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604544, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604597, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604598, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604650, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604688, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604725, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604727, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604755, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604756, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604806, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604856, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604857, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604905, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604940, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604987, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073604990, "dur": 62, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605054, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605056, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605097, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605137, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605139, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605173, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605175, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605218, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605219, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605265, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605269, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605314, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605315, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605366, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605371, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605413, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605415, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605517, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605519, "dur": 49, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605573, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605575, "dur": 58, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605637, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605639, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605680, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605684, "dur": 88, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605775, "dur": 13, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605809, "dur": 62, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605874, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605878, "dur": 60, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605941, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073605953, "dur": 55, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606015, "dur": 14, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606031, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606078, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606079, "dur": 126, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606208, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606215, "dur": 50, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606371, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606381, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606449, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073606630, "dur": 3209, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073609843, "dur": 712, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073610558, "dur": 25074, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073635635, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073635637, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073635829, "dur": 3593, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073639425, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073639612, "dur": 1100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073640715, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073640855, "dur": 4151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073645009, "dur": 771, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073645782, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073645784, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073645831, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073645886, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073646100, "dur": 1031, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647134, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647293, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647537, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647582, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647587, "dur": 259, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647848, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073647850, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073648163, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073648165, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073648226, "dur": 169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073648396, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073648398, "dur": 633, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073649032, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073649034, "dur": 302, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073649339, "dur": 311, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073649652, "dur": 1007, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073650661, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073650755, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073650865, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073651074, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073651076, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073651353, "dur": 384, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073651740, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073651980, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652199, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652351, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652438, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652544, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652652, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652867, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073652869, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653004, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653009, "dur": 493, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653505, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653691, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653729, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073653882, "dur": 147, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654047, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654048, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654167, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654350, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654453, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654761, "dur": 152, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654918, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073654923, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655119, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655252, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655356, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655437, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655495, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655616, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655729, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655735, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655920, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655924, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655989, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073655991, "dur": 544, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656542, "dur": 331, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656875, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656877, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656910, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073656916, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657077, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657178, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657262, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657348, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657354, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657533, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657679, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657681, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657738, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657739, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657946, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657982, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073657988, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658194, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658196, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658319, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658438, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658683, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658685, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073658727, "dur": 362, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659091, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659092, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659197, "dur": 470, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659670, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659755, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659886, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073659922, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660019, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660020, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660141, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660219, "dur": 261, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660483, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660666, "dur": 182, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660852, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073660917, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661027, "dur": 243, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661272, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661274, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661349, "dur": 526, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661876, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661907, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073661909, "dur": 379, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662292, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662326, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662328, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662455, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662521, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662598, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662633, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662741, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662745, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073662793, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663120, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663235, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663328, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663353, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663520, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663614, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663849, "dur": 29, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663882, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663969, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073663971, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664190, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664434, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664457, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664500, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664666, "dur": 293, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664961, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073664963, "dur": 75254, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740226, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740230, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740273, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740326, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740354, "dur": 18, "ph": "X", "name": "ReadAsync 7768", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740375, "dur": 15, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740391, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740432, "dur": 15, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073740448, "dur": 1174, "ph": "X", "name": "ProcessMessages 2951", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073741626, "dur": 1796, "ph": "X", "name": "ReadAsync 2951", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073743426, "dur": 13, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073743441, "dur": 20238, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073763685, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073763689, "dur": 2612, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073766304, "dur": 683, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073766991, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073766995, "dur": 813, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073767812, "dur": 347, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073768161, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073768162, "dur": 475, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073768642, "dur": 366, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073769013, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073769404, "dur": 394, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073769801, "dur": 1338, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073771142, "dur": 2892, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073774036, "dur": 1430, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073775469, "dur": 2476, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073777946, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073777947, "dur": 538, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073778488, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073778555, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073778560, "dur": 3445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073782014, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073782019, "dur": 750, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073782775, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073782778, "dur": 1527, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073784308, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073784438, "dur": 17, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073784458, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073784797, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073784802, "dur": 1125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073785933, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073785938, "dur": 646, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073786586, "dur": 12, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073786605, "dur": 1093, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073787702, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073787705, "dur": 1938, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073789649, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073789653, "dur": 564, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073790220, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073790223, "dur": 299, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073790527, "dur": 844, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073791376, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073791380, "dur": 1506, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073792889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073792892, "dur": 2027, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073794924, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073794928, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073795478, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073795480, "dur": 748, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073796232, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073796374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073796377, "dur": 1653, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798036, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798042, "dur": 550, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798597, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798754, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073798757, "dur": 773, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073799536, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073799539, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073799720, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073799723, "dur": 888, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073800617, "dur": 473, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073801096, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073801107, "dur": 1121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073802233, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073802237, "dur": 1042, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073803283, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073803284, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073803366, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073803369, "dur": 1983, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073805358, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073805362, "dur": 546, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073805910, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073805911, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806033, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806083, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806266, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806362, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806502, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806621, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806623, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806749, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806896, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073806905, "dur": 167, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807074, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807173, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807281, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807283, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807394, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807477, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807549, "dur": 70, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807621, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807651, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807695, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807732, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807780, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807837, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073807965, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808027, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808127, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808129, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808236, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808363, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808463, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808550, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073808551, "dur": 974, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809527, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809532, "dur": 33, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809568, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809617, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809648, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809747, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809864, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809866, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073809897, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810043, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810048, "dur": 368, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810418, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810420, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810476, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810564, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810643, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073810644, "dur": 453, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811101, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811188, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811212, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811375, "dur": 258, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811637, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811694, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073811766, "dur": 386, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812155, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812178, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812293, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812295, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812430, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073812437, "dur": 76519, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073888964, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073888967, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889011, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889013, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889056, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889092, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889095, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889131, "dur": 26, "ph": "X", "name": "ProcessMessages 6079", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889159, "dur": 30, "ph": "X", "name": "ReadAsync 6079", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889194, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889223, "dur": 32, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889258, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889292, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889329, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889368, "dur": 13, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073889383, "dur": 14122, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073903511, "dur": 5, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073903517, "dur": 363, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073903883, "dur": 49, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073903934, "dur": 183, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073904119, "dur": 270, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 85224, "tid": 12884901888, "ts": 1753863073904396, "dur": 2018, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073913779, "dur": 3434, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 85224, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 85224, "tid": 8589934592, "ts": 1753863073585610, "dur": 96332, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 85224, "tid": 8589934592, "ts": 1753863073681945, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 85224, "tid": 8589934592, "ts": 1753863073681955, "dur": 1015, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073917222, "dur": 56, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 85224, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 85224, "tid": 4294967296, "ts": 1753863073528846, "dur": 378728, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 85224, "tid": 4294967296, "ts": 1753863073531925, "dur": 50261, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 85224, "tid": 4294967296, "ts": 1753863073907626, "dur": 2315, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 85224, "tid": 4294967296, "ts": 1753863073908905, "dur": 68, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 85224, "tid": 4294967296, "ts": 1753863073910001, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073917281, "dur": 46, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753863073587575, "dur": 1049, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073588627, "dur": 348, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073589038, "dur": 124, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073589411, "dur": 1612, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_8BDFCCCCF2F213F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753863073591275, "dur": 998, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_FD39756FD6CA55CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753863073589171, "dur": 12765, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073601943, "dur": 302887, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073904980, "dur": 471, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753863073589063, "dur": 12894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073602020, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_277ABCF478EE8B6F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073602144, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073602238, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E1667A2214AEF83B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073602491, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073602585, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_47BFEF338AC2654E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073602808, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073602909, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_67842D246E217B48.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073603108, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073603201, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_34D2A3EE7210005C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073603382, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073603482, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_1180591D344D8020.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073603683, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073603786, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_AE7D1B3C4C95EA39.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073603969, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073604075, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_920C751F77AE822A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073604283, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073604420, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_8A92622E399500BD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073604663, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073604792, "dur": 6567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073611359, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073611448, "dur": 24738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073636186, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073636501, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_7FD1AEB5942D0F15.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073636604, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073636712, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073638459, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073640269, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073642065, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073642839, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073643619, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073645387, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073645842, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073646740, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073647024, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073648460, "dur": 9990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073658450, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073658642, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073659516, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073661387, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073661714, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073662655, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073662769, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073663325, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753863073663604, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073664189, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073665250, "dur": 77204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073742460, "dur": 26333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073768794, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073768994, "dur": 17693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073786687, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073786766, "dur": 10294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/UnityMcpBridge.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073797060, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073797233, "dur": 9381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753863073806615, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073806723, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073806790, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073806853, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073806968, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807031, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807145, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807209, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807410, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807619, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807805, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073807960, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808071, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808131, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808188, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808451, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808513, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808626, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808755, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808893, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073808970, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073809026, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753863073809080, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073809535, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073809620, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073809693, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753863073809743, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073809984, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073810165, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073810256, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073810528, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073810654, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073810796, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073811352, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073812212, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073812265, "dur": 92001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753863073904266, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753863073904341, "dur": 403, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753863073904745, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073589069, "dur": 12898, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073602014, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_2FC4566222D5E975.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073602167, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073602286, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_239AFA330BBAC656.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073602513, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073602620, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_398D16DC2F388968.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073602820, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073602955, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2D277F65E8A998F3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073603139, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073603240, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_F8C9484306133139.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073603403, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073603510, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_80E55A7F23C3B352.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073603712, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073603827, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_FDD8D1D32CFB3B53.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073603998, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073604104, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_0FD4B971BC7A85D5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073604284, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073604467, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073604607, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_12BEA117667A4829.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073604789, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073604901, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605034, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605164, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605283, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605431, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605576, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605731, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605832, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073605960, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606101, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606248, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606380, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606526, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606660, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606768, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606879, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073606994, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073607138, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073607263, "dur": 1772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073609035, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073610832, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073612646, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073614472, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073616367, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073618186, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073620070, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073622036, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073623788, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073625549, "dur": 1879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073627428, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073629225, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073631046, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073632769, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073634566, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073636363, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073638142, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073639951, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073641714, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073643347, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073645089, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073645864, "dur": 859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073646723, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073647114, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073648558, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073649902, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073649976, "dur": 4349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073654325, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073654619, "dur": 1706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073656362, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073658555, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073658802, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753863073659630, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073660629, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073660715, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073661405, "dur": 3853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073665258, "dur": 77214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073742477, "dur": 24530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073767007, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073767160, "dur": 18357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073785518, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073785620, "dur": 16187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073801808, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753863073801908, "dur": 10553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753863073812536, "dur": 92280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073589070, "dur": 12903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073601975, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_8F35D61FA1350848.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073602091, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073602214, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A2445FA7364886EC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073602443, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073602546, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_28576C1B0DCFAD46.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073602761, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073602866, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_43C6B06C79ACF706.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073603076, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073603173, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_DC02F97A155BDF20.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073603353, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073603444, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_D4A3E2A4FD17346E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073603659, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073603763, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3ABFCB27EB850015.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073603948, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073604036, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_FEB988CCF0E6BB15.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073604245, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073604392, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073604619, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073604739, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073604870, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605007, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605138, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605270, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605418, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605569, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605715, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605818, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073605950, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606079, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606207, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606345, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606493, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606632, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606740, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606846, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073606968, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073607107, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073607226, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073608992, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073610796, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073612592, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073614403, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073616264, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073618135, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073620011, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073621978, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073623738, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073625497, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073627356, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073629141, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073630978, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073632699, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073634487, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073636290, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073638041, "dur": 1830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073639871, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073641641, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073643288, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073645008, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073646232, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073646675, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073647018, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073647788, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073647857, "dur": 5561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073653419, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073653688, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073653796, "dur": 2085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073655882, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073655982, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073658315, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073658434, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753863073659286, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073660432, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073660513, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753863073661384, "dur": 637, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073741041, "dur": 316, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073662243, "dur": 79123, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1753863073742450, "dur": 25241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073767692, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073767824, "dur": 11000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073778825, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073778943, "dur": 8377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073787321, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073787454, "dur": 12993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073800448, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073800586, "dur": 11296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753863073811882, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073812058, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753863073813094, "dur": 91737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073589081, "dur": 12897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073601981, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_0A6B6D9C47F4530B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073602115, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073602225, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_47C7297E7D9BA8E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073602456, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073602562, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_FFD7B8C1FB3EAC30.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073602776, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073602882, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_2C1796C793713FC4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073603091, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073603186, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0D0B9ACADB00965C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073603368, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073603454, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_008BA9569D204B9D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073603671, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073603769, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_B9CE8C468A9B7594.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073603969, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073604060, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_18C2C716AD449AF1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073604282, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073604430, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_F37438DA78C7A2C5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073604665, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073604795, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073604924, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605052, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605173, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605303, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605454, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605605, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605736, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605842, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073605971, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606099, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606234, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606371, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606511, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606641, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606751, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606865, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073606978, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073607109, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073607237, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073609021, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073610826, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073612630, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073614459, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073616342, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073618150, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073620033, "dur": 1971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073622005, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073623773, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073625535, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073627415, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073629214, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073631030, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073632759, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073634536, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073636324, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073638074, "dur": 1824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073639898, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073641680, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073643312, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073645046, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073645662, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073646122, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073646710, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073647558, "dur": 1519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073649077, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073649170, "dur": 3062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073652233, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073652385, "dur": 4239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073656624, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073656815, "dur": 1615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073658431, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073659612, "dur": 1104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073660716, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073660812, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073661388, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073661938, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073662168, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073663338, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073663412, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073663687, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073664413, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073664479, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073664789, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753863073664859, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073665081, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073665253, "dur": 77233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073742488, "dur": 28999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073771487, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073771581, "dur": 13373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073784954, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073785049, "dur": 15239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073800288, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753863073800372, "dur": 12189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753863073812632, "dur": 92191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073589083, "dur": 12906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073601994, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_9D626291F6D8AF71.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073602150, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073602268, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A06AA2A1BED86839.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073602499, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073602610, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_C802029EED0CA831.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073602816, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073602916, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F3B942ABEAB27F4C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073603118, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073603217, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_99197D166ACE50E7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073603388, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073603496, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_7AD49193F34FA244.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073603705, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073603819, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_82867387D6B3635F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073604000, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073604115, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_002FD5D937D9DC6F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073604321, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073604475, "dur": 6109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073610585, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073610705, "dur": 29321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073640026, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073640313, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_C1F0FBEBCB4A09D3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073640409, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073640503, "dur": 5264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073645768, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073645864, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073646752, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073647020, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073648801, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073648861, "dur": 3677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073652538, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073652713, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073652774, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073655201, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073655281, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073658139, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073658238, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073658440, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073658649, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073659500, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073659567, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073661827, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073661931, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073662146, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073663157, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753863073663519, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073664119, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073665255, "dur": 77216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073742474, "dur": 27722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073770197, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073770315, "dur": 13185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073783501, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073783616, "dur": 17696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073801312, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073801425, "dur": 9953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753863073811501, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753863073812558, "dur": 92269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073589096, "dur": 12901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073601999, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_0B403034A7AD700F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073602178, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073602297, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64DBB6C5202FCB97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073602521, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073602636, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C979F7BDD315F95F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073602826, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073602961, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_009A86653CA42335.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073603142, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073603249, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_F798AB981FE153BE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073603406, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073603526, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_44C9E78231B79A26.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073603714, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073603842, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_853CADB02D114EB5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073604005, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073604127, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073604263, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F8B1325CE934834A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073604499, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073604638, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073604752, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073604934, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605067, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605199, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605325, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605487, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605639, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605751, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073605873, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606000, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606132, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606283, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606413, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606558, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606675, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606784, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073606899, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073607027, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073607163, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073607289, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073609070, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073610623, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073612449, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073614232, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073616131, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073617971, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073619843, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073621797, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073623587, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073625324, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073627164, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073628968, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073630828, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073632551, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073634327, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073636105, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073637900, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073639698, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073641518, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073641603, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753863073641661, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073641746, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073642680, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073643441, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073645204, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073645674, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073646068, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073646712, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073647033, "dur": 3006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073650079, "dur": 3926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073654005, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073654168, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_CAA37C6A1EE81358.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753863073654244, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073654438, "dur": 3165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityMcpBridge.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073657603, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073657733, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073657807, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073658437, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073658664, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073660560, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073661404, "dur": 3863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073665267, "dur": 77200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073742470, "dur": 26071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073768541, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073768660, "dur": 19712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/UnityMcpBridge.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073788373, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073788541, "dur": 7671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073796213, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073796341, "dur": 15506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753863073811847, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073812012, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753863073813059, "dur": 91778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073589098, "dur": 12905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073602004, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_AFD95CE7FC8EBA7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073602176, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073602294, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_8BDFCCCCF2F213F3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073602515, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073602628, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_ADCEADF44A51D0EE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073602818, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073602943, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9015C2D9E3FB0D6E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073603131, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073603230, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_59173AFBB9D2CD71.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073603390, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073603504, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3F9153727B21C62F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073603702, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073603809, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_EC6FAE50E8680E3A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073603982, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073604090, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C4CB382400E8CF88.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073604282, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073604450, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073604600, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_5DDC931FF17671DE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073604805, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073604931, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605060, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605181, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605314, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605474, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605626, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605738, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605856, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073605983, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606111, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606258, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606387, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606533, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606665, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606779, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073606889, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073607010, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073607135, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073607259, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073609045, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073610855, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073612663, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073614486, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073616369, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073618176, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073620062, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073622020, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073623795, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073625551, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073627422, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073629227, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073631048, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073632780, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073634572, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073636332, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073638080, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073639905, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073641686, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073643321, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073645067, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073645826, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073646186, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073646731, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073647022, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073648529, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073648591, "dur": 4271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073652862, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073653035, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073655241, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073657870, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073657989, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073658455, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073658651, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073659699, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073659759, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073660959, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073661013, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073661392, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073662659, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073662762, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073663145, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073663225, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753863073663504, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073664176, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073664252, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073665252, "dur": 77209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073742466, "dur": 27538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073770005, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073770122, "dur": 20866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073790989, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073791084, "dur": 13039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073804124, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753863073804225, "dur": 8776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753863073813068, "dur": 91785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073589110, "dur": 12898, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073602009, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_153F404E0D8F558E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073602164, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073602282, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_82F1B2D8CA597BA6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073602510, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073602616, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_C5E02F3EAA031E4E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073602818, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073602926, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5AA19FA6C4CF5E30.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073603127, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073603221, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2C18E212B0E4132D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073603389, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073603498, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D2ABBEBE1FC4C37.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073603690, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073603790, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3F3A4E99D5F3FCB2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073603973, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073604086, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_03C5BC0B9E82FC86.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073604284, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073604440, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073604641, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073604761, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073604882, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605003, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073605197, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605321, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605478, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605630, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605750, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605876, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073605990, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606121, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606270, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606402, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606548, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606672, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606782, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073606895, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073607021, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073607156, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073607277, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073609052, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073610861, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073612658, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073614479, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073616355, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073618169, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073620054, "dur": 1974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073622028, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073623780, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073625548, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073627413, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073629223, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073631050, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073632775, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073634555, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073636341, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073638095, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073639926, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073641706, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073643293, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073645018, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073645534, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073645995, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073646702, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073647033, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/UnityMcpBridge.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073648175, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073648235, "dur": 3724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/UnityMcpBridge.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073651959, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073652115, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073652182, "dur": 1629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/UnityMcpBridge.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073653812, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073653878, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073655994, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073656077, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073658831, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073659045, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073660535, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073661408, "dur": 3834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073665249, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753863073665390, "dur": 77056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073742457, "dur": 21914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073764372, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073764515, "dur": 14769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073779284, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073779410, "dur": 20082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073799492, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073799597, "dur": 12555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753863073812238, "dur": 92016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753863073904266, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753863073904255, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753863073904348, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073589112, "dur": 12900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073602018, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6A560D90654712F2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073602149, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073602256, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_4A49E4F0525A2A8A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073602493, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073602597, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_C753D39C258C8E4F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073602803, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073602901, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_B389E5E896DCE4DA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073603107, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073603197, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_0E9BBF368F534C2A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073603379, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073603466, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_267247B37BB2AB38.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073603685, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073603797, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6E8DEEC477D92ABF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073603954, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073604043, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_DD4AEEF3EF14569E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073604261, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073604400, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073604645, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073604750, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073604880, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073604998, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_E8E7F11947F4E560.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073605197, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073605318, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073605478, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073605632, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073605745, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073605883, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606005, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606144, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606293, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606426, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606569, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606684, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606787, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073606907, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073607037, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073607177, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073607301, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073609055, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073610859, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073612672, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073614505, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073616380, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073618199, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073620084, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073622053, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073623804, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073625563, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073627431, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073629235, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073631059, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073632788, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073634584, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073636359, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073638117, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073639934, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073641696, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073643340, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073645081, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073645651, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073646036, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073646691, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073647112, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073648097, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073648154, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073649571, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073649631, "dur": 6573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073656204, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073656501, "dur": 1729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073658260, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073658447, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073658656, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073659723, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073660806, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073660916, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073661390, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073661760, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073662586, "dur": 2657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073665257, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753863073665405, "dur": 77637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073743048, "dur": 27409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.DocCodeExamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073770458, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073770746, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753863073770679, "dur": 20546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073791687, "dur": 7162, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073889751, "dur": 284, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753863073799258, "dur": 90785, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753863073904273, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753863073904262, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753863073904344, "dur": 417, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753863073589125, "dur": 12929, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073602054, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_E50469BFBC801034.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073602238, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073602374, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_7B1F8AF01189A2CD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073602573, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073602726, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_865CA694956B9BBD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073602906, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073603064, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5664E0E259B230B5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073603201, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073603328, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_5130F10CCE7F6FDC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073603486, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073603629, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C61F6A869DC02419.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073603795, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073603929, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_90E1AFE28D7FE085.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073604079, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073604237, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_38AE9428D0945E79.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073604458, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073604620, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073604752, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073604901, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605018, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073605225, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605364, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605521, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605677, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605776, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073605919, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606031, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606184, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606331, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606464, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606615, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606720, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606831, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073606953, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073607090, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073607215, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073609008, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073610809, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073612609, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073614433, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073616314, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073618148, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073620047, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073622012, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073623786, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073625540, "dur": 1862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073627402, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073629204, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073631023, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073632752, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073634530, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073636312, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073638072, "dur": 1845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073639917, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073641693, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073643329, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073645058, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073645663, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073646106, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073646682, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073647077, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073648464, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073648529, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073649111, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073650528, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073650593, "dur": 4689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073655282, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073655440, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_7CDC6BD5ACEADF98.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073655566, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073655635, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073655722, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073655790, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073656512, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073656602, "dur": 1841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073658443, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073658654, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073659690, "dur": 1327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073661017, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073661094, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073661397, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073663160, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753863073663471, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073663945, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073664011, "dur": 1252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073665263, "dur": 77218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073742484, "dur": 26854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073769339, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073769497, "dur": 8197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073777695, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073777802, "dur": 15774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073793577, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073793754, "dur": 12323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073806077, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753863073806187, "dur": 7101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753863073813307, "dur": 91513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073589127, "dur": 12894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073602028, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_988B7925B2E3C9FD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073602149, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073602259, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_0E10F0AC38116458.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073602492, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073602590, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_AE7ED900728C7CF0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073602805, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073602903, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_FD39756FD6CA55CF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073603106, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073603192, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_6AE7FCD125A11414.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073603378, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073603461, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_83A02020CC9D94F1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073603682, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073603780, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_5C6C568518A6ED31.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073603969, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073604068, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_90A50BF7CD91167B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073604266, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073604438, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C31C6642F2780EE7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073604663, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073604785, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073604900, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605029, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073605226, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605375, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605530, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605682, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605786, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073605924, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606040, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606192, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606326, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606456, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606602, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606715, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606827, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073606943, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073607076, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073607201, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073608985, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073610784, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073612598, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073614395, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073616247, "dur": 1866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073618113, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073619989, "dur": 1961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073621950, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073623728, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073625483, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073627344, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073629148, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073630985, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073632707, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073634500, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073636294, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073638048, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073639864, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073641629, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073643279, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073645005, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073646223, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073646704, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073647031, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073649259, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073649338, "dur": 3844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073653182, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073653407, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073653550, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073655497, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073658021, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073658169, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073658441, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073658656, "dur": 1857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073660568, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073661400, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753863073661545, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073662357, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073662436, "dur": 2826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073665262, "dur": 78836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073744098, "dur": 30813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073774911, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073775091, "dur": 16979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073792071, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073792217, "dur": 18984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753863073811201, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073811311, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073812013, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073812082, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753863073813187, "dur": 91656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073589139, "dur": 12892, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073602035, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_AAD7FA5E0D946403.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073602184, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073602311, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_A3BB870842DF4825.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073602529, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073602655, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_10794DC432733AA8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073602837, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073602977, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3BDC0CD2452B03AE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073603142, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073603257, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_CE628D2C7B770067.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073603409, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073603534, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_AA3EB9D44969AE42.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073603715, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073603846, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_6028511849507570.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073604012, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073604133, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073604283, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_371873F0BAD0C8AE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073604499, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073604651, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073604795, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073604914, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605044, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605168, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605300, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605443, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605592, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605734, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605852, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073605964, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606094, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606217, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606357, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606503, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606639, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606746, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606864, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073606973, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073607113, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073607241, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073609028, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073610835, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073612640, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073614446, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073616305, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073618140, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073620024, "dur": 1964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073621988, "dur": 1772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073623760, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073625519, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073627376, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073629183, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073631006, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073632720, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073634490, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073636292, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073638051, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073639890, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073641666, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073643305, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073645036, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073645760, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073646189, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073646670, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073647037, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073648777, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073651416, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073651629, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073651696, "dur": 1987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073653733, "dur": 4834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073658567, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073658870, "dur": 1661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073660531, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073661398, "dur": 1933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073663331, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073663649, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073664766, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073664845, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073665251, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753863073665356, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073665557, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073666059, "dur": 78156, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073769966, "dur": 7401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073777367, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073777503, "dur": 12525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073790670, "dur": 8755, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073889757, "dur": 497, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753863073799672, "dur": 90588, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753863073904253, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753863073904343, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073589141, "dur": 12892, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073602036, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_413C46072143215F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073602179, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073602300, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_23B44CC7B1A2DD42.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073602522, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073602650, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A8613260465BFA1A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073602837, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073602963, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_8D76E0E30D4C7E1D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073603141, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073603241, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5A2F0A5C60EAA3F0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073603404, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073603519, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_681ED338E6649C18.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073603714, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073603832, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_E6A6871A06F0D6C7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073603996, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073604096, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_CD7A202FD176738C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073604286, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073604459, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073604638, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073604766, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073604886, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605010, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073605200, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605351, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605511, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605660, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605772, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073605904, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606015, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606165, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606317, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606446, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606585, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606697, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606811, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073606920, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073607053, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073607190, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073607317, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073609064, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073610882, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073612675, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073614513, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073616377, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073618221, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073620090, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073622055, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073623797, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073625558, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073627425, "dur": 1812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073629237, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073631064, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073632795, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073634593, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073636373, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073638133, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073639944, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073641722, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073643376, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073645093, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073645766, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073646018, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073646732, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073647029, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073648004, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073648069, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073650138, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073650326, "dur": 4158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073654484, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073654739, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073654812, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073654947, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073655034, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073655117, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073656732, "dur": 2335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073659067, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073659179, "dur": 1353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073660532, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073661398, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753863073661535, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073662290, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073662391, "dur": 2853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073665244, "dur": 18532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073683983, "dur": 135, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 13, "ts": 1753863073684118, "dur": 913, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 13, "ts": 1753863073685032, "dur": 480, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 13, "ts": 1753863073683776, "dur": 1738, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073685514, "dur": 56934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073742453, "dur": 28070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073770524, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073770624, "dur": 19760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073790385, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073790478, "dur": 12504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073802982, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753863073803072, "dur": 10087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753863073813178, "dur": 91657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073589145, "dur": 12903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073602050, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_9D9467EAA9DEAB55.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073602225, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073602354, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_3EE2E62A78DDACE4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073602566, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073602709, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_52960AB27BC76A4C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073602882, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073603015, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DD7C41366C07F0E8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073603173, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073603285, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_E72C49B126E908C0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073603442, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073603574, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6361AB9143CF0BF0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073603749, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073603880, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AAB884C075C2EB66.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073604032, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073604173, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073604327, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_A4E15FBBA990B2E4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073604537, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073604699, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073604822, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073604959, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605096, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605230, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605378, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605546, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605702, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605795, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073605934, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606050, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606200, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606338, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606481, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606625, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606739, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606843, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073606966, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073607104, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073607231, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073609016, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073610813, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073612617, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073614426, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073616303, "dur": 1843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073618147, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073620038, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073622010, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073623768, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073625523, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073627387, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073629194, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073631009, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073632732, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073634515, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073636318, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073638065, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073639883, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073641657, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073643299, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073645011, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073646230, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073646709, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073647054, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073647526, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073647590, "dur": 5928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073653519, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073653762, "dur": 3837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073657599, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073657764, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073658432, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073658657, "dur": 1869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073660613, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073661399, "dur": 3018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073664417, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753863073664484, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073664726, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073665245, "dur": 20272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073685517, "dur": 57059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073742579, "dur": 27129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073769708, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073769853, "dur": 21404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073791258, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073791391, "dur": 12129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073803520, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753863073803616, "dur": 9382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753863073813084, "dur": 91758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073589159, "dur": 12886, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073602048, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_BAAD893008DD2D7B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073602211, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073602322, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_73F4B21D3E23B00A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073602545, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073602675, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D1294CA07F644899.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073602852, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073602992, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_86F68F4D76BCEB7A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073603166, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073603270, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_0FECCBB95249A89A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073603430, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073603552, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_1AD224C572DE948B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073603744, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073603870, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_C92D44EB54EA3A74.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073604024, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073604157, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073604320, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_95EA5B5208A442EA.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073604505, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073604672, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073604808, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073604934, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605093, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605216, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605349, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605496, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605645, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605766, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073605896, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606010, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606164, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606302, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606439, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606586, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606707, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606818, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073606929, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073607074, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073607189, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073609002, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073610798, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073612599, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073614419, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073616291, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073618130, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073620013, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073621970, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073623733, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073625489, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073627347, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073629165, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073630991, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073632714, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073634482, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073636255, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073638015, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073639841, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073641614, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073643274, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073644192, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073645880, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073646719, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073647027, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073649236, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073649298, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073651704, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073651866, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073651937, "dur": 4061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073655998, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073656204, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073656284, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073656465, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753863073656566, "dur": 1863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073658429, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073659527, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073661019, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073661121, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073661391, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753863073661794, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073662709, "dur": 2541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073665250, "dur": 78823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073744092, "dur": 30566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073774658, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073775035, "dur": 10090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073785125, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073785252, "dur": 11258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073796510, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073796631, "dur": 13788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753863073810420, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073810540, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753863073810592, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073810862, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073812029, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073812117, "dur": 1189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753863073813327, "dur": 91522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073589165, "dur": 12887, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073602052, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_0417CDDB0E014CF7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073602243, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073602377, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_8EE3F6994C37E841.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073602598, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073602743, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_9781225C20A5F334.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073602912, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073603062, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_8C47F0B6E91DBC9B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073603201, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073603312, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_62E702BE2A8C1163.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073603468, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073603620, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7E3734150E3C6AFB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073603779, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073603902, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_BA1A73E3478366C9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073604046, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073604188, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C31EE0B03FE82B24.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073604405, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073604575, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073604710, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073604845, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073604987, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605114, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605250, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605400, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605560, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605709, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605804, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073605941, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606069, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606226, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606368, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606516, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606652, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606759, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606870, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073606985, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073607126, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073607252, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073609040, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073610848, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073612651, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073614465, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073616353, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073618162, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073620069, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073622026, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073623781, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073625542, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073627406, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073629201, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073631016, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073632744, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073634521, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073636297, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073638063, "dur": 1830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073639893, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073641673, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073643324, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073645044, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073645767, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073645999, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073646698, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073647043, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073648494, "dur": 4445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073652939, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073653184, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_129BD977D0A9204D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073653291, "dur": 1646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073654980, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073657284, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073657410, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753863073658042, "dur": 1925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aEDbg.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073659968, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073660097, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073660563, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073661401, "dur": 3867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073665268, "dur": 78518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073743791, "dur": 28183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073771975, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073772086, "dur": 10635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073782722, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073782811, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073782886, "dur": 12689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073795576, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073795753, "dur": 12693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753863073808447, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073808547, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073808751, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073808843, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073808971, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809146, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809226, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809309, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809364, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809450, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809618, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073809901, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810064, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810293, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1753863073810346, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810453, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810567, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810715, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073810818, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073811500, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073811553, "dur": 1057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753863073812655, "dur": 92160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753863073906312, "dur": 621, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 85224, "tid": 853, "ts": 1753863073918413, "dur": 3163, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 85224, "tid": 853, "ts": 1753863073921680, "dur": 1394, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 85224, "tid": 853, "ts": 1753863073912486, "dur": 11131, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}